import pandas as pd
import numpy as np

# Load the CSV file
df = pd.read_csv('./simple_price_action_trades_30min.csv')

print('=== RISK-TO-REWARD ANALYSIS ===')
print(f'Total trades: {len(df)}')

# Separate winning and losing trades
winning_trades = df[df['Profit Points'] > 0]
losing_trades = df[df['Profit Points'] < 0]

print(f'Winning trades: {len(winning_trades)}')
print(f'Losing trades: {len(losing_trades)}')

# Calculate averages
avg_winning_trade = winning_trades['Profit Points'].mean() if len(winning_trades) > 0 else 0
avg_losing_trade = abs(losing_trades['Profit Points'].mean()) if len(losing_trades) > 0 else 0

print(f'Average winning trade: {avg_winning_trade:.2f} points')
print(f'Average losing trade: {avg_losing_trade:.2f} points')

# Calculate Risk-to-Reward Ratio
risk_reward_ratio = avg_winning_trade / avg_losing_trade if avg_losing_trade != 0 else float('inf')
print(f'Risk-to-Reward Ratio: {risk_reward_ratio:.2f}')

# Calculate Win Rate
win_rate = (len(winning_trades) / len(df)) * 100
print(f'Win Rate: {win_rate:.2f}%')

# Calculate Expectancy
expectancy = (win_rate / 100 * avg_winning_trade) - ((100 - win_rate) / 100 * avg_losing_trade)
print(f'Expectancy: {expectancy:.2f} points per trade')

# Breakdown by exit reason
print('\n=== BREAKDOWN BY EXIT REASON ===')
exit_reasons = df['Exit Reason'].value_counts()
print(exit_reasons)

# Analyze Take Profit vs Stop Loss specifically
take_profit_trades = df[df['Exit Reason'] == 'Take Profit']
stop_loss_trades = df[df['Exit Reason'] == 'Stop Loss']

if len(take_profit_trades) > 0:
    avg_take_profit = take_profit_trades['Profit Points'].mean()
    print(f'\nAverage Take Profit: {avg_take_profit:.2f} points')

if len(stop_loss_trades) > 0:
    avg_stop_loss = abs(stop_loss_trades['Profit Points'].mean())
    print(f'Average Stop Loss: {avg_stop_loss:.2f} points')
    
    if len(take_profit_trades) > 0:
        tp_sl_ratio = avg_take_profit / avg_stop_loss
        print(f'Take Profit to Stop Loss Ratio: {tp_sl_ratio:.2f}')

# Additional statistics
print(f'\n=== ADDITIONAL STATISTICS ===')
print(f'Total profit: {df["Profit Points"].sum():.2f} points')
print(f'Best trade: {df["Profit Points"].max():.2f} points')
print(f'Worst trade: {df["Profit Points"].min():.2f} points')

# Calculate profit factor
gross_profit = winning_trades['Profit Points'].sum()
gross_loss = abs(losing_trades['Profit Points'].sum())
profit_factor = gross_profit / gross_loss if gross_loss != 0 else float('inf')
print(f'Profit factor: {profit_factor:.2f}')

# Year-by-year breakdown if data spans multiple years
df['Entry DateTime'] = pd.to_datetime(df['Entry DateTime'])
df['Year'] = df['Entry DateTime'].dt.year
years = sorted(df['Year'].unique())

if len(years) > 1:
    print(f'\n=== YEARLY BREAKDOWN ===')
    for year in years:
        year_data = df[df['Year'] == year]
        year_wins = year_data[year_data['Profit Points'] > 0]
        year_losses = year_data[year_data['Profit Points'] < 0]
        
        if len(year_wins) > 0 and len(year_losses) > 0:
            year_avg_win = year_wins['Profit Points'].mean()
            year_avg_loss = abs(year_losses['Profit Points'].mean())
            year_rr = year_avg_win / year_avg_loss
            year_win_rate = (len(year_wins) / len(year_data)) * 100
            
            print(f'{year}: R:R = {year_rr:.2f}, Win Rate = {year_win_rate:.1f}%, Trades = {len(year_data)}')