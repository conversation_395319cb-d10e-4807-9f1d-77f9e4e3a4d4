import numpy as np
import pandas as pd
import yaml
import datetime
from backtesting import Strategy
from SimplePriceActionCore import PriceActionPatterns

def load_config(config_path="SimplePriceActionStrategyConfig.yaml"):
    """Load configuration from YAML file."""
    try:
        with open(config_path, 'r') as file:
            config = yaml.safe_load(file)
        return config
    except Exception as e:
        print(f"Error loading configuration: {e}")
        exit()

def calculate_ema(series, period):
    """Calculate Exponential Moving Average."""
    return pd.Series(series).ewm(span=period, adjust=False).mean()

def calculate_rsi(series, period=14):
    """Calculate Relative Strength Index."""
    delta = pd.Series(series).diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    avg_gain = gain.rolling(window=period).mean()
    avg_loss = loss.rolling(window=period).mean()
    
    # Handle division by zero
    rs = avg_gain / avg_loss.replace(0, np.finfo(float).eps)
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_atr(high, low, close, period):
    """Calculate Average True Range."""
    tr1 = pd.Series(high) - pd.Series(low)
    tr2 = abs(pd.Series(high) - pd.Series(close).shift(1))
    tr3 = abs(pd.Series(low) - pd.Series(close).shift(1))
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    atr = tr.rolling(period).mean()
    return atr

class SimplePriceActionStrategy(Strategy):
    """
    A simple price action strategy that combines basic indicators and price patterns.
    With advanced entry filters for improved trade selection.
    """
    
    def init(self):
        """Initialize strategy parameters and indicators."""
        # Load strategy parameters from config
        config = load_config()
        self.strategy_config = config['strategy']
        
        # Initialize trade log
        self.trade_log = []
        
        # Trading session times
        self.market_start_time = self.strategy_config['market_start_time']
        self.market_end_time = self.strategy_config['market_end_time']
        self.entry_start_time = self.strategy_config['entry_start_time']
        self.exit_end_time = self.strategy_config['exit_end_time']
        
        # Intraday-only mode settings
        self.intraday_only = self.strategy_config.get('intraday_only', False)
        self.force_exit_time = self.strategy_config.get('force_exit_time', '15:15')
        self.stop_new_trades_time = self.strategy_config.get('stop_new_trades_time', '15:00')
        self.aggressive_eod_exit = self.strategy_config.get('aggressive_eod_exit', False)
        
        # Ensure force_exit_time is before market_end_time for intraday-only mode
        if self.intraday_only:
            force_hour, force_minute = map(int, self.force_exit_time.split(':'))
            market_end_hour, market_end_minute = map(int, self.market_end_time.split(':'))
            
            force_minutes = force_hour * 60 + force_minute
            market_end_minutes = market_end_hour * 60 + market_end_minute
            
            if force_minutes >= market_end_minutes:
                # Adjust force_exit_time to be 15 minutes before market_end_time
                new_force_minutes = market_end_minutes - 15
                new_force_hour = new_force_minutes // 60
                new_force_minute = new_force_minutes % 60
                self.force_exit_time = f"{new_force_hour:02d}:{new_force_minute:02d}"
                print(f"Warning: force_exit_time adjusted to {self.force_exit_time} (15 minutes before market close)")
        
        # Risk management parameters
        self.sl_atr_multiplier = self.strategy_config['sl_atr_multiplier']
        self.tp_rr = self.strategy_config['tp_rr']
        self.position_size = self.strategy_config['position_size']
        
        # Load time filter parameters
        self.time_filters = self.strategy_config.get('time_filters', {})
        self.lunch_hour_start = self.time_filters.get('lunch_hour_start', '12:00')
        self.lunch_hour_end = self.time_filters.get('lunch_hour_end', '13:00')
        self.hour_transition_avoid_minutes = self.time_filters.get('hour_transition_avoid_minutes', 5)
        self.market_opening_duration_minutes = self.time_filters.get('market_opening_duration_minutes', 15)
        
        # Load price action filter parameters
        self.pa_config = self.strategy_config['price_action']
        self.pa_signal_threshold_multiplier = self.pa_config.get('signal_threshold_multiplier', 1.2)
        self.volatility_check_multiplier = self.pa_config.get('volatility_check_multiplier', 0.5)
        
        # Load choppy market parameters
        self.choppy_market_config = self.pa_config.get('choppy_market', {})
        self.choppy_market_lookback = self.choppy_market_config.get('lookback_bars', 5)
        self.choppy_market_threshold = self.choppy_market_config.get('range_percent_threshold', 0.10)
        
        # Load trade management parameters
        self.trade_mgmt = self.strategy_config.get('trade_management', {})
        
        # Trailing stop parameters
        self.trailing_stop_config = self.trade_mgmt.get('trailing_stop', {})
        self.trailing_stop_profit_thresholds = self.trailing_stop_config.get('profit_thresholds', [0.5, 0.3, 0.2])
        self.trailing_stop_lock_in_percentages = self.trailing_stop_config.get('lock_in_percentages', [0.7, 0.5, 0.3])
        
        # Time-based exit parameters
        self.time_exit_config = self.trade_mgmt.get('time_based_exit', {})
        self.long_duration_minutes = self.time_exit_config.get('long_duration_minutes', 30)
        self.long_duration_profit_percent = self.time_exit_config.get('long_duration_profit_percent', 0.15)
        self.medium_duration_minutes = self.time_exit_config.get('medium_duration_minutes', 15)
        self.medium_duration_profit_percent = self.time_exit_config.get('medium_duration_profit_percent', 0.25)
        self.loss_cut_minutes = self.time_exit_config.get('loss_cut_minutes', 10)
        self.loss_cut_percent = self.time_exit_config.get('loss_cut_percent', -0.3)
        self.loss_cut_duration_minutes = self.time_exit_config.get('loss_cut_duration_minutes', 5)  # New parameter
        
        # Take profit calculation parameters
        self.tp_config = self.trade_mgmt.get('take_profit', {})
        self.recent_price_lookback = self.tp_config.get('recent_price_lookback', 20)
        self.distance_multiplier = self.tp_config.get('distance_multiplier', 1.5)
        self.atr_buffer = self.tp_config.get('atr_buffer', 0.2)
        
        # Technical analysis parameters
        self.tech_analysis = self.strategy_config.get('technical_analysis', {})
        
        # Trend calculation parameters
        self.trend_calc = self.tech_analysis.get('trend_calculation', {})
        self.slope_periods = self.trend_calc.get('slope_periods', 5)
        self.min_data_points = self.trend_calc.get('min_data_points', 10)
        
        # Average ATR parameters
        self.avg_atr = self.tech_analysis.get('average_atr', {})
        self.atr_default_lookback = self.avg_atr.get('default_lookback', 100)
        
        # Key levels parameters
        self.key_levels_config = self.tech_analysis.get('key_levels', {})
        self.key_levels_lookback = self.key_levels_config.get('default_lookback', 120)
        self.key_levels_num = self.key_levels_config.get('default_num_levels', 5)
        self.key_levels_window = self.key_levels_config.get('window_size', 5)
        self.key_levels_clustering = self.key_levels_config.get('clustering_threshold', 0.001)
        
        # Initialize indicators
        self._init_indicators()
        
        # Initialize price action indicators
        self._init_price_action_indicators()
        
        # Trading state variables
        self.in_trade = False
        self.entry_price = 0
        self.stop_loss = 0
        self.take_profit = 0
        self.entry_time = None
        self.position_type = None  # 'long' or 'short'
        
        # Debug mode
        self.debug = config['logging']['enable_debug']
        
    def _init_indicators(self):
        """Initialize technical indicators."""
        # Moving averages for trend identification
        self.fast_ma = self.I(calculate_ema, self.data.Close, self.strategy_config['fast_ma_period'])
        self.slow_ma = self.I(calculate_ema, self.data.Close, self.strategy_config['slow_ma_period'])
        
        # RSI for overbought/oversold conditions
        self.rsi = self.I(calculate_rsi, self.data.Close, self.strategy_config['rsi_period'])
        
        # ATR for volatility measurement and stop loss calculation
        self.atr = self.I(calculate_atr, self.data.High, self.data.Low, self.data.Close, 
                          self.strategy_config['atr_period'])
        
        # Long-term trend indicator for advanced entry filters
        if 'entry_filters' in self.strategy_config and self.strategy_config['entry_filters'].get('enable_advanced_filters', False):
            trend_period = self.strategy_config['entry_filters'].get('trend_filter_period', 50)
            self.long_term_ma = self.I(calculate_ema, self.data.Close, trend_period)
    
    def _init_price_action_indicators(self):
        """Initialize price action pattern indicators."""
        # Create price action patterns detector with configuration
        self.pa_patterns = PriceActionPatterns("SimplePriceActionStrategyConfig.yaml")
        
        # For engulfing patterns (returns bullish and bearish)
        engulfing_result = self.I(self.pa_patterns.detect_engulfing, 
                                 self.data.Open, self.data.Close, 
                                 self.data.High, self.data.Low)
        # Convert to single array: +1 for bullish, -1 for bearish
        self.engulfing = self.I(lambda bull, bear: np.where(bull, 1.0, np.where(bear, -1.0, 0.0)),
                               engulfing_result[0], engulfing_result[1])
        
        # For pin bars (returns bullish and bearish)
        pin_bars_result = self.I(self.pa_patterns.detect_pin_bars,
                                self.data.Open, self.data.Close,
                                self.data.High, self.data.Low)
        # Convert to single array
        self.pin_bars = self.I(lambda bull, bear: np.where(bull, 1.0, np.where(bear, -1.0, 0.0)),
                              pin_bars_result[0], pin_bars_result[1])
        
        # Inside bars (returns boolean array)
        inside_bars_result = self.I(self.pa_patterns.detect_inside_bars,
                                   self.data.High, self.data.Low)
        # Convert to numeric array (neutral signal)
        self.inside_bars = self.I(lambda x: np.where(x, 0.5, 0.0), inside_bars_result)
        
        # Outside bars (returns boolean array)
        outside_bars_result = self.I(self.pa_patterns.detect_outside_bars,
                                    self.data.High, self.data.Low)
        # Convert to numeric array (neutral signal)
        self.outside_bars = self.I(lambda x: np.where(x, 0.5, 0.0), outside_bars_result)
        
        # For breakout detection
        # The function returns a tuple of (bullish_breakouts, bearish_breakouts)
        breakout_result = self.I(self.pa_patterns.detect_breakout_strength,
                               self.data.Open, self.data.Close,
                               self.data.High, self.data.Low,
                               self.data.Volume,
                               self.strategy_config['breakout_lookback'])
        
        # Convert to single array: +1 for bullish, -1 for bearish
        self.breakouts = self.I(lambda bull, bear: np.where(bull, 1.0, np.where(bear, -1.0, 0.0)),
                              breakout_result[0], breakout_result[1])
        
        # Calculate combined price action signal manually
        weights = [
            self.strategy_config['price_action']['weight_engulfing'],
            self.strategy_config['price_action']['weight_pinbar'],
            self.strategy_config['price_action']['weight_inside_bar'],
            self.strategy_config['price_action']['weight_outside_bar'],
            self.strategy_config['price_action']['weight_breakout']
        ]
        
        # Manually combine signals with weights
        total_weight = sum(weights)
        self.pa_signal = self.I(
            lambda eng, pin, ins, out, brk: (
                (eng * weights[0] + 
                 pin * weights[1] + 
                 ins * weights[2] + 
                 out * weights[3] + 
                 brk * weights[4]) / total_weight
            ),
            self.engulfing, self.pin_bars, self.inside_bars, 
            self.outside_bars, self.breakouts
        )
    
    def _calculate_entry_signal(self):
        """Calculate entry signal based on indicators and price action."""
        # Default: no signal
        signal = 0
        
        # Check if we're in the valid trading time window
        current_time = self.data.index[-1].strftime('%H:%M')
        
        # Check if we're in the valid trading time window
        if not (self.entry_start_time <= current_time <= self.exit_end_time):
            return signal
            
        # Avoid lunch hour if configured
        if self.strategy_config.get('avoid_lunch_hour', False) and self.lunch_hour_start <= current_time <= self.lunch_hour_end:
            return signal
            
        # Additional time-based filter: Avoid trading in the first X minutes of each hour
        # This helps avoid choppy price action during hourly transitions
        minute = int(current_time.split(':')[1])
        if 0 <= minute < self.hour_transition_avoid_minutes:
            return signal
        
        # Trend direction from moving averages
        trend_up = self.fast_ma[-1] > self.slow_ma[-1]
        trend_down = self.fast_ma[-1] < self.slow_ma[-1]
        
        # RSI momentum - check if RSI is moving in the expected direction
        rsi_up = self.rsi[-1] > self.rsi[-2]
        rsi_down = self.rsi[-1] < self.rsi[-2]
        
        # Price action signal strength
        pa_signal_strength = self.pa_signal[-1]
        signal_threshold = self.strategy_config['price_action']['signal_threshold']
        
        # Volatility check - moderate requirement
        volatility_sufficient = self.atr[-1] > self.atr[-20:].mean() * self.volatility_check_multiplier
        
        # Price confirmation - check if price is making higher highs/lower lows
        price_confirmation_long = self.data.Close[-1] > self.data.Close[-2]
        price_confirmation_short = self.data.Close[-1] < self.data.Close[-2]
        
        # Check for choppy market - avoid trading when price is moving sideways
        last_n_closes = self.data.Close[-self.choppy_market_lookback:].astype(float)
        price_range = max(last_n_closes) - min(last_n_closes)
        avg_price = sum(last_n_closes) / len(last_n_closes)
        range_percent = (price_range / avg_price) * 100
        
        # If price range is too small relative to ATR, market might be choppy
        not_choppy = range_percent > self.choppy_market_threshold
        
        # Long signal conditions
        if (trend_up and  # Trend direction
            pa_signal_strength > signal_threshold and  # Price action signal
            volatility_sufficient and
            price_confirmation_long and  # Price confirmation
            not_choppy and  # Avoid choppy markets
            (rsi_up or not self.strategy_config['use_rsi_filter'])):  # RSI momentum
            
            # Additional filter: Check if we're not in overbought territory
            if not (self.rsi[-1] > self.strategy_config['rsi_overbought'] and self.strategy_config['use_rsi_filter']):
                signal = 1
        
        # Short signal conditions
        elif (trend_down and  # Trend direction
              pa_signal_strength < -signal_threshold and  # Price action signal
              volatility_sufficient and
              price_confirmation_short and  # Price confirmation
              not_choppy and  # Avoid choppy markets
              (rsi_down or not self.strategy_config['use_rsi_filter'])):  # RSI momentum
              
            # Additional filter: Check if we're not in oversold territory
            if not (self.rsi[-1] < self.strategy_config['rsi_oversold'] and self.strategy_config['use_rsi_filter']):
                signal = -1
        
        return signal
    
    def next(self):
        """Main strategy logic executed for each candle."""
        # Skip if we don't have enough data for indicators
        if self.fast_ma[-1] == np.nan or self.slow_ma[-1] == np.nan:
            return
        
        current_time = self.data.index[-1].strftime('%H:%M')
        current_date = self.data.index[-1].strftime('%Y-%m-%d')
        current_price = self.data.Close[-1]
        
        # Check if we need to exit based on time
        if current_time >= self.exit_end_time and self.position:
            if self.debug:
                print(f"Time-based exit at {current_time}")
            self._record_exit_details(self.data.index[-1], current_price, "Time-based Exit")
            self.position.close()
            self.in_trade = False
            return
            
        # Force exit for intraday-only mode - STRICT ENFORCEMENT
        if self.intraday_only and self.position:
            # Standard force exit at the specified time
            if current_time >= self.force_exit_time:
                if self.debug:
                    print(f"Intraday-only force exit at {current_time}")
                self._record_exit_details(self.data.index[-1], current_price, "Intraday Force Exit")
                self.position.close()
                self.in_trade = False
                return
                
            # Check if this is the last candle of the day by looking at the current time
            # If we're close to market end time, close the position
            market_end_hour, market_end_minute = map(int, self.market_end_time.split(':'))
            current_hour, current_minute = map(int, current_time.split(':'))
            
            current_minutes = current_hour * 60 + current_minute
            market_end_minutes = market_end_hour * 60 + market_end_minute
            
            # If we're within 30 minutes of market close, exit the position
            if market_end_minutes - current_minutes <= 30:
                if self.debug:
                    print(f"End of day exit at {current_time}, {market_end_minutes - current_minutes} minutes before market close")
                self._record_exit_details(self.data.index[-1], current_price, "End of Day Exit")
                self.position.close()
                self.in_trade = False
                return
                
            # Aggressive end-of-day exit (exit with smaller profit or if trade isn't working)
            if self.aggressive_eod_exit:
                # Calculate time remaining until force exit
                current_hour, current_minute = map(int, current_time.split(':'))
                force_hour, force_minute = map(int, self.force_exit_time.split(':'))
                
                current_minutes = current_hour * 60 + current_minute
                force_minutes = force_hour * 60 + force_minute
                minutes_remaining = force_minutes - current_minutes
                
                # If less than 30 minutes remaining and in profit, consider taking profit
                if minutes_remaining < 30 and self.position.pl > 0:
                    if self.debug:
                        print(f"Aggressive EOD exit with profit at {current_time}, {minutes_remaining} minutes before close")
                    self._record_exit_details(self.data.index[-1], current_price, "Aggressive EOD Profit Exit")
                    self.position.close()
                    self.in_trade = False
                    return
                    
                # If less than 15 minutes remaining and not moving in our favor, cut losses
                if minutes_remaining < 15 and self.position.pl < 0:
                    if self.debug:
                        print(f"Aggressive EOD exit with loss at {current_time}, {minutes_remaining} minutes before close")
                    self._record_exit_details(self.data.index[-1], current_price, "Aggressive EOD Loss Exit")
                    self.position.close()
                    self.in_trade = False
                    return
            
        # Check if we're in a position that was opened on a previous day (for intraday-only mode)
        if self.intraday_only and self.position and self.in_trade:
            entry_date = self.entry_time.strftime('%Y-%m-%d')
            if entry_date != current_date:
                if self.debug:
                    print(f"Closing overnight position at {current_time}")
                self._record_exit_details(self.data.index[-1], current_price, "Overnight Exit")
                self.position.close()
                self.in_trade = False
                return
        
        # If we're in a trade, check for exit conditions
        if self.in_trade and self.position:
            # Calculate profit in points
            if self.position_type == 'long':
                profit_points = current_price - self.entry_price
                profit_percent = (profit_points / self.entry_price) * 100
            else:  # short
                profit_points = self.entry_price - current_price
                profit_percent = (profit_points / self.entry_price) * 100
            
            # Implement progressive trailing stop loss
            # The more profit we have, the tighter the trailing stop
            trailing_percent = 0  # Default: no trailing stop
            
            # Check profit thresholds in descending order
            for i, threshold in enumerate(self.trailing_stop_profit_thresholds):
                if profit_percent > threshold:
                    trailing_percent = self.trailing_stop_lock_in_percentages[i]
                    break
                
            if trailing_percent > 0:
                    if self.position_type == 'long':
                        # Calculate trailing stop: max(initial stop, entry + X% of profit)
                        trailing_stop = max(
                            self.stop_loss,
                            self.entry_price + (profit_points * trailing_percent)
                        )
                        # Update stop loss if trailing stop is higher
                        if trailing_stop > self.stop_loss:
                            self.stop_loss = trailing_stop
                    else:  # short
                        # Calculate trailing stop: min(initial stop, entry - X% of profit)
                        trailing_stop = min(
                            self.stop_loss,
                            self.entry_price - (profit_points * trailing_percent)
                        )
                        # Update stop loss if trailing stop is lower
                        if trailing_stop < self.stop_loss:
                            self.stop_loss = trailing_stop
            
            # Check stop loss
            if (self.position_type == 'long' and current_price <= self.stop_loss) or \
               (self.position_type == 'short' and current_price >= self.stop_loss):
                if self.debug:
                    print(f"Stop loss hit at {current_price}")
                self._record_exit_details(self.data.index[-1], current_price, "Stop Loss")
                self.position.close()
                self.in_trade = False
                return
            
            # Check take profit
            if (self.position_type == 'long' and current_price >= self.take_profit) or \
               (self.position_type == 'short' and current_price <= self.take_profit):
                if self.debug:
                    print(f"Take profit hit at {current_price}")
                self._record_exit_details(self.data.index[-1], current_price, "Take Profit")
                self.position.close()
                self.in_trade = False
                return
                
            # Add time-based partial profit taking with improved logic
            # If we've been in the trade for more than X minutes and have profit
            trade_duration = self.data.index[-1] - self.entry_time
            
            # Scale the profit threshold based on time in trade
            minutes_in_trade = trade_duration.total_seconds() / 60
            
            # For longer trades, accept smaller profits
            if minutes_in_trade > self.long_duration_minutes and profit_percent > self.long_duration_profit_percent:
                if self.debug:
                    print(f"Time-based profit taking (long duration) at {current_price}")
                self._record_exit_details(self.data.index[-1], current_price, "Time-based Profit")
                self.position.close()
                self.in_trade = False
                return
            elif minutes_in_trade > self.medium_duration_minutes and profit_percent > self.medium_duration_profit_percent:
                if self.debug:
                    print(f"Time-based profit taking (medium duration) at {current_price}")
                self._record_exit_details(self.data.index[-1], current_price, "Time-based Profit")
                self.position.close()
                self.in_trade = False
                return
            
            # Cut losses faster if trade is moving against us after some time
            if minutes_in_trade > self.loss_cut_minutes and profit_percent < self.loss_cut_percent:
                if self.debug:
                    print(f"Cutting losses after time threshold at {current_price}")
                self._record_exit_details(self.data.index[-1], current_price, "Time-based Loss Cut")
                self.position.close()
                self.in_trade = False
                return
            
        # If we're not in a trade, check for entry conditions
        if not self.in_trade:
            signal = self._calculate_entry_signal()
            
            # Long entry
            if signal > 0:
                # Apply advanced entry filters
                if not self.should_enter_trade(self.data, 'LONG'):
                    if self.debug:
                        print(f"Long entry rejected by advanced filters")
                    return
                
                self.entry_price = current_price
                
                # Calculate stop loss based on ATR
                self.stop_loss = current_price - (self.atr[-1] * self.sl_atr_multiplier)
                
                # Calculate stop loss distance
                sl_distance = self.atr[-1] * self.sl_atr_multiplier

                # Fixed take profit calculation for consistent 1:2 risk-reward ratio
                # Always use the standard calculation to ensure proper risk-reward
                self.take_profit = current_price + (sl_distance * self.tp_rr)
                
                self.entry_time = self.data.index[-1]
                self.position_type = 'long'
                
                # Enter long position
                self.buy(size=self.position_size)
                self.in_trade = True
                
                if self.debug:
                    print(f"Long entry at {current_price}, SL: {self.stop_loss}, TP: {self.take_profit}")
                
                # Record trade details
                self._record_entry_details('LONG', self.entry_time, self.entry_price, 
                                          self.stop_loss, self.take_profit, self.pa_signal[-1])
            
            # Short entry
            elif signal < 0:
                # Apply advanced entry filters
                if not self.should_enter_trade(self.data, 'SHORT'):
                    if self.debug:
                        print(f"Short entry rejected by advanced filters")
                    return
                
                self.entry_price = current_price
                
                # Calculate stop loss based on ATR
                self.stop_loss = current_price + (self.atr[-1] * self.sl_atr_multiplier)
                
                # Calculate stop loss distance
                sl_distance = self.atr[-1] * self.sl_atr_multiplier

                # Fixed take profit calculation for consistent 1:2 risk-reward ratio
                # Always use the standard calculation to ensure proper risk-reward
                self.take_profit = current_price - (sl_distance * self.tp_rr)
                
                self.entry_time = self.data.index[-1]
                self.position_type = 'short'
                
                # Enter short position
                self.sell(size=self.position_size)
                self.in_trade = True
                
                if self.debug:
                    print(f"Short entry at {current_price}, SL: {self.stop_loss}, TP: {self.take_profit}")
                
                # Record trade details
                self._record_entry_details('SHORT', self.entry_time, self.entry_price, 
                                          self.stop_loss, self.take_profit, self.pa_signal[-1])
    
    def _record_entry_details(self, entry_type, entry_datetime, entry_price, sl_price, tp_price, pa_signal=0.0):
        """Record entry details for trade log."""
        # Get formatting settings from config
        datetime_format = self.strategy_config.get('output_formatting', {}).get('datetime_format', '%Y-%m-%d %H:%M:%S')
        decimal_precision = self.strategy_config.get('output_formatting', {}).get('decimal_precision', 2)
        
        # Determine which price action pattern had the highest contribution
        pattern_values = {
            "Engulfing": self.engulfing[-1],
            "Pin Bar": self.pin_bars[-1],
            "Inside Bar": self.inside_bars[-1],
            "Outside Bar": self.outside_bars[-1],
            "Breakout": self.breakouts[-1]
        }
        
        # Get the weights from config
        weights = [
            self.strategy_config['price_action']['weight_engulfing'],
            self.strategy_config['price_action']['weight_pinbar'],
            self.strategy_config['price_action']['weight_inside_bar'],
            self.strategy_config['price_action']['weight_outside_bar'],
            self.strategy_config['price_action']['weight_breakout']
        ]
        
        # Calculate weighted values
        weighted_values = {
            "Engulfing": pattern_values["Engulfing"] * weights[0],
            "Pin Bar": pattern_values["Pin Bar"] * weights[1],
            "Inside Bar": pattern_values["Inside Bar"] * weights[2],
            "Outside Bar": pattern_values["Outside Bar"] * weights[3],
            "Breakout": pattern_values["Breakout"] * weights[4]
        }
        
        # Find the pattern with the highest weighted contribution
        if entry_type == 'LONG':
            # For long trades, find the pattern with the most positive contribution
            dominant_pattern = max(weighted_values.items(), key=lambda x: x[1])[0]
        else:  # SHORT
            # For short trades, find the pattern with the most negative contribution
            dominant_pattern = min(weighted_values.items(), key=lambda x: x[1])[0]
        
        trade = {
            "Index": len(self.trade_log) + 1,
            "Entry DateTime": entry_datetime.strftime(datetime_format),
            "Entry Price": round(entry_price, decimal_precision),
            "Stop Loss": round(sl_price, decimal_precision),
            "Take Profit": round(tp_price, decimal_precision),
            "Position": entry_type,
            "PA Signal": round(pa_signal, decimal_precision),
            "Price Action Pattern": dominant_pattern,  # Add the dominant pattern
            "Exit DateTime": None,
            "Exit Price": None,
            "Profit Points": None,
            "Profit Percent": None,
            "Trade Duration": None,
            "Exit Reason": None
        }
        self.trade_log.append(trade) 
    # After (suggested change for calculate_trend):
    def calculate_trend(self, candles=None, period=None): # 'candles' is often not needed if using self.data or pre-calculated indicators
        """
        Calculate the trend direction based on the slope of the long-term moving average.
        
        Args:
            period: Period for the moving average calculation (optional, uses config value if None)
                This parameter is largely superseded by self.long_term_ma's period.
                It's kept for signature consistency if needed elsewhere.
                
        Returns:
            float: Positive value for uptrend, negative for downtrend, magnitude indicates strength
        """
        # Ensure long_term_ma is initialized and has enough data
        if not hasattr(self, 'long_term_ma') or len(self.long_term_ma) < self.slope_periods:
            return 0  # Not enough data or advanced filters not enabled for long_term_ma

        # Get the last 'self.slope_periods' values of the long-term MA
        # backtesting.py series support direct slicing from the end
        ma_values_for_slope = self.long_term_ma[-self.slope_periods:]

        if len(ma_values_for_slope) < self.slope_periods or np.isnan(ma_values_for_slope).any():
            return 0  # Not enough valid data points for slope calculation

        # Ensure the starting point for percentage change is not zero to avoid ZeroDivisionError
        if ma_values_for_slope[0] == 0:
            return 0

        # Simple slope calculation (percentage change)
        return (ma_values_for_slope[-1] - ma_values_for_slope[0]) / ma_values_for_slope[0] * 100

    def calculate_average_atr(self, candles, lookback=None):
        """
        Calculate the average ATR over a lookback period.
        
        Args:
            candles: DataFrame or Series of price data
            lookback: Number of periods to look back (optional, uses config value if None)
            
        Returns:
            float: Average ATR value
        """
        if lookback is None:
            lookback = self.atr_default_lookback
            
        if len(self.atr) < lookback:
            return self.atr[-1]  # Not enough data, return current ATR
            
        return np.mean(self.atr[-lookback:])
    
    def identify_key_levels(self, candles, lookback=None, num_levels=None):
        """
        Identify key support and resistance levels.
        
        Args:
            candles: DataFrame or Series of price data
            lookback: Number of periods to look back (optional, uses config value if None)
            num_levels: Number of key levels to identify (optional, uses config value if None)
            
        Returns:
            list: List of key price levels
        """
        # Use config values if parameters are not provided
        if lookback is None:
            lookback = self.key_levels_lookback
        if num_levels is None:
            num_levels = self.key_levels_num
            
        # In Backtesting.py, we need to access data differently
        # We'll use the last N values of High and Low directly
        
        # Limit lookback to available data
        lookback = min(lookback, len(self.data.High))
        if lookback < self.min_data_points:  # Need enough data for meaningful analysis
            return []
            
        # Extract high and low prices for the last lookback periods
        highs = []
        lows = []
        
        for i in range(lookback):
            idx = -lookback + i
            highs.append(self.data.High[idx])
            lows.append(self.data.Low[idx])
            
        highs = np.array(highs)
        lows = np.array(lows)
        
        # Find local maxima and minima
        resistance_levels = []
        support_levels = []
        
        # Simple approach: look for bars where the high/low is higher/lower than surrounding bars
        window = self.key_levels_window  # Look at N bars on each side
        
        for i in range(window, len(highs) - window):
            # Check if this bar's high is higher than all bars in the window
            if highs[i] > max(highs[i-window:i]) and highs[i] > max(highs[i+1:i+window+1]):
                resistance_levels.append(highs[i])
                
            # Check if this bar's low is lower than all bars in the window
            if lows[i] < min(lows[i-window:i]) and lows[i] < min(lows[i+1:i+window+1]):
                support_levels.append(lows[i])
        
        # Cluster similar levels
        clustered_levels = []
        all_levels = sorted(resistance_levels + support_levels)
        
        if not all_levels:
            return []
            
        current_cluster = [all_levels[0]]
        
        for level in all_levels[1:]:
            if abs(level - current_cluster[-1]) / current_cluster[-1] < self.key_levels_clustering:
                current_cluster.append(level)
            else:
                # Add average of current cluster
                clustered_levels.append(sum(current_cluster) / len(current_cluster))
                current_cluster = [level]
                
        # Add the last cluster
        if current_cluster:
            clustered_levels.append(sum(current_cluster) / len(current_cluster))
            
        # Return the most significant levels (limit to num_levels)
        return sorted(clustered_levels)[:num_levels]
    
    def is_near_key_level(self, price, key_levels, threshold):
        """
        Check if the current price is near a key support/resistance level.
        
        Args:
            price: Current price
            key_levels: List of key price levels
            threshold: Distance threshold (e.g., ATR value)
            
        Returns:
            bool: True if price is near a key level, False otherwise
        """
        if not key_levels:
            return False
            
        for level in key_levels:
            if abs(price - level) <= threshold:
                return True
                
        return False
    
    def is_lunch_hour(self, current_time):
        """
        Check if the current time is during lunch hour.
        
        Args:
            current_time: Current time as a datetime.time object
            
        Returns:
            bool: True if it's lunch hour, False otherwise
        """
        if not self.strategy_config.get('avoid_lunch_hour', False):
            return False
            
        # Convert string to time if needed
        if isinstance(current_time, str):
            hour, minute = map(int, current_time.split(':'))
            current_time = datetime.time(hour, minute)
            
        # Parse lunch hour times from config
        lunch_start_hour, lunch_start_minute = map(int, self.lunch_hour_start.split(':'))
        lunch_end_hour, lunch_end_minute = map(int, self.lunch_hour_end.split(':'))
        
        lunch_start = datetime.time(lunch_start_hour, lunch_start_minute)
        lunch_end = datetime.time(lunch_end_hour, lunch_end_minute)
        
        return lunch_start <= current_time <= lunch_end
    
    def is_market_opening(self, current_time):
        """
        Check if the current time is during market opening (first X minutes).
        
        Args:
            current_time: Current time as a datetime.time object
            
        Returns:
            bool: True if it's during market opening, False otherwise
        """
        # Convert string to time if needed
        if isinstance(current_time, str):
            hour, minute = map(int, current_time.split(':'))
            current_time = datetime.time(hour, minute)
            
        market_start = datetime.datetime.strptime(self.market_start_time, '%H:%M').time()
        
        # Calculate market start + X minutes
        market_start_dt = datetime.datetime.combine(datetime.date.today(), market_start)
        market_start_plus_duration = (market_start_dt + datetime.timedelta(minutes=self.market_opening_duration_minutes)).time()
        
        return market_start <= current_time <= market_start_plus_duration

    def should_enter_trade(self, candles, position):
        """
        Apply advanced filters to determine if a trade should be entered.
        
        Args:
            candles: DataFrame or Series of price data
            position: 'LONG' or 'SHORT'
            
        Returns:
            bool: True if all filters pass, False otherwise
        """
        # Check if advanced filters are enabled
        if not self.strategy_config.get('entry_filters', {}).get('enable_advanced_filters', False):
            return True  # Skip filtering if not enabled
        
        # Basic price action signal from your existing strategy
        pa_signal = self.pa_signal[-1]
        signal_threshold = self.strategy_config['price_action']['signal_threshold']
        
        # 1. Check if signal strength meets threshold
        if abs(pa_signal) < signal_threshold:
            if self.debug:
                print(f"Filter failed: Signal strength {pa_signal} below threshold {signal_threshold}")
            return False
        
        # Get filter toggle settings
        entry_filters = self.strategy_config.get('entry_filters', {})
        enable_trend_filter = entry_filters.get('enable_trend_filter', True)
        enable_volatility_filter = entry_filters.get('enable_volatility_filter', True)
        enable_key_level_filter = entry_filters.get('enable_key_level_filter', True)
        enable_time_filter = entry_filters.get('enable_time_filter', True)
        
        # 2. Trend filter
        if enable_trend_filter:
            trend_filter_period = entry_filters.get('trend_filter_period', 50)
            long_term_trend = self.calculate_trend(candles, period=trend_filter_period)
            
            if (position == 'LONG' and long_term_trend < 0) or (position == 'SHORT' and long_term_trend > 0):
                if self.debug:
                    print(f"Filter failed: Trend direction ({long_term_trend}) doesn't match position ({position})")
                return False
        
        # 3. Volatility filter
        if enable_volatility_filter:
            current_atr = self.atr[-1]
            avg_atr = self.calculate_average_atr(candles)  # Uses default from config
            volatility_multiplier = entry_filters.get('volatility_multiplier', 2.0)
            
            if current_atr > avg_atr * volatility_multiplier:  # Too volatile
                if self.debug:
                    print(f"Filter failed: Current volatility ({current_atr}) > {volatility_multiplier}x average ({avg_atr})")
                return False
        
        # 4. Support/Resistance proximity check
        if enable_key_level_filter:
            key_levels = self.identify_key_levels(candles)  # Uses defaults from config
            current_price = self.data.Close[-1]
            current_atr = self.atr[-1]
            key_level_threshold = entry_filters.get('key_level_threshold', 1.0) * current_atr
            
            if self.is_near_key_level(current_price, key_levels, threshold=key_level_threshold):
                if self.debug:
                    print(f"Filter failed: Price {current_price} is too close to a key level")
                return False
        
        # 5. Time-based filters
        if enable_time_filter:
            current_time = self.data.index[-1].strftime('%H:%M')
            
            # Check if it's too late in the day for new trades (intraday-only mode)
            if self.intraday_only:
                # Don't enter trades after the stop_new_trades_time
                if current_time >= self.stop_new_trades_time:
                    if self.debug:
                        print(f"Filter failed: Too late for new trades at {current_time}, cutoff is {self.stop_new_trades_time}")
                    return False
                
                # Calculate time remaining until force exit
                current_hour, current_minute = map(int, current_time.split(':'))
                force_hour, force_minute = map(int, self.force_exit_time.split(':'))
                
                current_minutes = current_hour * 60 + current_minute
                force_minutes = force_hour * 60 + force_minute
                minutes_remaining = force_minutes - current_minutes
                
                # Ensure there's enough time for the trade to develop
                # Don't enter if less than 45 minutes remain until force exit
                if minutes_remaining < 45:
                    if self.debug:
                        print(f"Filter failed: Only {minutes_remaining} minutes remain until force exit at {self.force_exit_time}")
                    return False
            
            # Check other time-based filters
            if self.is_lunch_hour(current_time) or self.is_market_opening(current_time):
                if self.debug:
                    print(f"Filter failed: Time-based filter at {current_time}")
                return False
        
        # All filters passed, take the trade
        if self.debug:
            print(f"All filters passed for {position} entry")
        return True
    
    def _record_exit_details(self, exit_datetime, exit_price, custom_exit_reason=None):
        """Record exit details for trade log."""
        if not self.trade_log:
            return
        
        # Get formatting settings from config
        datetime_format = self.strategy_config.get('output_formatting', {}).get('datetime_format', '%Y-%m-%d %H:%M:%S')
        decimal_precision = self.strategy_config.get('output_formatting', {}).get('decimal_precision', 2)
        
        # Get the last trade
        trade = self.trade_log[-1]
        
        # Update exit details
        trade["Exit DateTime"] = exit_datetime.strftime(datetime_format)
        trade["Exit Price"] = round(exit_price, decimal_precision)
        
        # Calculate profit
        entry_price = trade["Entry Price"]
        if trade["Position"] == "LONG":
            profit_points = exit_price - entry_price
        else:  # SHORT
            profit_points = entry_price - exit_price
        
        profit_percent = (profit_points / entry_price) * 100 if entry_price != 0 else 0
        
        trade["Profit Points"] = round(profit_points, decimal_precision)
        trade["Profit Percent"] = round(profit_percent, decimal_precision)
        
        # Calculate duration
        from datetime import datetime
        entry_dt = datetime.strptime(trade["Entry DateTime"], datetime_format)
        exit_dt = datetime.strptime(trade["Exit DateTime"], datetime_format)
        trade["Trade Duration"] = str(exit_dt - entry_dt)
        
        # Determine exit reason
        if custom_exit_reason:
            trade["Exit Reason"] = custom_exit_reason
        elif exit_datetime.strftime('%H:%M') >= self.exit_end_time:
            trade["Exit Reason"] = "Time-based Exit"
        elif (trade["Position"] == "LONG" and exit_price <= trade["Stop Loss"]) or \
             (trade["Position"] == "SHORT" and exit_price >= trade["Stop Loss"]):
            trade["Exit Reason"] = "Stop Loss"
        elif (trade["Position"] == "LONG" and exit_price >= trade["Take Profit"]) or \
             (trade["Position"] == "SHORT" and exit_price <= trade["Take Profit"]):
            trade["Exit Reason"] = "Take Profit"
        else:
            trade["Exit Reason"] = "Unknown Exit"