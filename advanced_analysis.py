import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

# Load the CSV file
#df = pd.read_csv('Backup_SimplePriceActionStrategy/V7_IntradayCleanup_Best_30Min/simple_price_action_trades_30min.csv')
df = pd.read_csv('./simple_price_action_trades.csv')

# Convert profit points to absolute values for analysis
df['Abs_Profit_Points'] = df['Profit Points'].abs()

# Create a new column to identify winning and losing trades
df['Trade_Result'] = df['Profit Points'].apply(lambda x: 'Win' if x > 0 else 'Loss')

# Analyze stop loss distribution by percentiles
stop_loss_trades = df[df['Exit Reason'] == 'Stop Loss']
stop_loss_points = stop_loss_trades['Abs_Profit_Points']

percentiles = [10, 25, 50, 75, 90, 95]
sl_percentiles = np.percentile(stop_loss_points, percentiles)

print("Stop Loss Distribution (percentiles):")
for i, p in enumerate(percentiles):
    print(f"{p}th percentile: {sl_percentiles[i]:.2f} points")

# Analyze take profit distribution by percentiles
take_profit_trades = df[df['Exit Reason'] == 'Take Profit']
take_profit_points = take_profit_trades['Profit Points']

tp_percentiles = np.percentile(take_profit_points, percentiles)

print("\nTake Profit Distribution (percentiles):")
for i, p in enumerate(percentiles):
    print(f"{p}th percentile: {tp_percentiles[i]:.2f} points")

# Analyze the relationship between stop loss size and win rate
# Create stop loss size bins
stop_loss_bins = [0, 2, 4, 6, 8, 10, 15, 20, float('inf')]
df['SL_Size_Bin'] = pd.cut(df[df['Exit Reason'] == 'Stop Loss']['Abs_Profit_Points'], 
                           bins=stop_loss_bins, 
                           labels=['0-2', '2-4', '4-6', '6-8', '8-10', '10-15', '15-20', '20+'])

# Count trades in each bin
sl_bin_counts = df['SL_Size_Bin'].value_counts().sort_index()
print("\nStop Loss Size Distribution:")
print(sl_bin_counts)

# Analyze the relationship between take profit size and win rate
# Create take profit size bins
take_profit_bins = [0, 2, 4, 6, 8, 10, 15, 20, float('inf')]
df['TP_Size_Bin'] = pd.cut(df[df['Exit Reason'] == 'Take Profit']['Profit Points'], 
                           bins=take_profit_bins, 
                           labels=['0-2', '2-4', '4-6', '6-8', '8-10', '10-15', '15-20', '20+'])

# Count trades in each bin
tp_bin_counts = df['TP_Size_Bin'].value_counts().sort_index()
print("\nTake Profit Size Distribution:")
print(tp_bin_counts)

# Analyze the relationship between trade duration and profitability
df['Trade Duration'] = pd.to_timedelta(df['Trade Duration'])
df['Duration Seconds'] = df['Trade Duration'].dt.total_seconds()

# Create duration bins (in seconds)
duration_bins = [0, 60, 120, 180, 300, 600, 1200, float('inf')]
duration_labels = ['0-1m', '1-2m', '2-3m', '3-5m', '5-10m', '10-20m', '20m+']
df['Duration_Bin'] = pd.cut(df['Duration Seconds'], bins=duration_bins, labels=duration_labels)

# Calculate win rate and average profit by duration bin
duration_analysis = df.groupby('Duration_Bin').agg({
    'Profit Points': ['count', 'mean'],
    'Trade_Result': lambda x: (x == 'Win').mean() * 100
}).reset_index()

duration_analysis.columns = ['Duration_Bin', 'Trade_Count', 'Avg_Profit', 'Win_Rate']
print("\nProfitability by Trade Duration:")
print(duration_analysis)

# Analyze the relationship between time of day and profitability
df['Entry DateTime'] = pd.to_datetime(df['Entry DateTime'])
df['Hour'] = df['Entry DateTime'].dt.hour

# Calculate win rate and average profit by hour
hour_analysis = df.groupby('Hour').agg({
    'Profit Points': ['count', 'mean'],
    'Trade_Result': lambda x: (x == 'Win').mean() * 100
}).reset_index()

hour_analysis.columns = ['Hour', 'Trade_Count', 'Avg_Profit', 'Win_Rate']
print("\nProfitability by Hour of Day:")
print(hour_analysis.sort_values('Win_Rate', ascending=False))

# Analyze 10-minute splits for lower profit hours
# First, identify the lower profit hours (bottom 50% by average profit)
median_profit = hour_analysis['Avg_Profit'].median()
lower_profit_hours = hour_analysis[hour_analysis['Avg_Profit'] <= median_profit]['Hour'].tolist()

print(f"\nLower profit hours (below median profit of {median_profit:.2f}): {lower_profit_hours}")

# Filter data for lower profit hours only
lower_hours_df = df[df['Hour'].isin(lower_profit_hours)].copy()

if not lower_hours_df.empty:
    # Extract minute from datetime
    lower_hours_df['Minute'] = lower_hours_df['Entry DateTime'].dt.minute
    
    # Create 10-minute bins
    minute_bins = [0, 10, 20, 30, 40, 50, 60]
    minute_labels = ['0-10min', '10-20min', '20-30min', '30-40min', '40-50min', '50-60min']
    lower_hours_df['Minute_Bin'] = pd.cut(lower_hours_df['Minute'], bins=minute_bins, labels=minute_labels, right=False)
    
    # Analyze profitability by 10-minute intervals within lower profit hours
    minute_analysis = lower_hours_df.groupby(['Hour', 'Minute_Bin']).agg({
        'Profit Points': ['count', 'mean', 'sum'],
        'Trade_Result': lambda x: (x == 'Win').mean() * 100
    }).reset_index()
    
    minute_analysis.columns = ['Hour', 'Minute_Bin', 'Trade_Count', 'Avg_Profit', 'Total_Profit', 'Win_Rate']
    
    print("\n10-Minute Split Analysis for Lower Profit Hours:")
    print("=" * 70)
    
    # Sort by hour and then by minute bin for better readability
    minute_analysis_sorted = minute_analysis.sort_values(['Hour', 'Minute_Bin'])
    
    for hour in sorted(lower_profit_hours):
        hour_data = minute_analysis_sorted[minute_analysis_sorted['Hour'] == hour]
        if not hour_data.empty:
            print(f"\nHour {hour}:00-{hour}:59:")
            print("-" * 40)
            for _, row in hour_data.iterrows():
                print(f"  {row['Minute_Bin']}: {row['Trade_Count']} trades, "
                      f"Avg Profit: {row['Avg_Profit']:.2f}, "
                      f"Total Profit: {row['Total_Profit']:.2f}, "
                      f"Win Rate: {row['Win_Rate']:.1f}%")
    
    # Overall summary for 10-minute intervals across all lower profit hours
    overall_minute_analysis = lower_hours_df.groupby('Minute_Bin').agg({
        'Profit Points': ['count', 'mean', 'sum'],
        'Trade_Result': lambda x: (x == 'Win').mean() * 100
    }).reset_index()
    
    overall_minute_analysis.columns = ['Minute_Bin', 'Trade_Count', 'Avg_Profit', 'Total_Profit', 'Win_Rate']
    
    print("\nOverall 10-Minute Performance Summary (Lower Profit Hours Only):")
    print("=" * 70)
    print(overall_minute_analysis.sort_values('Avg_Profit', ascending=False))
    
    # Find the best and worst 10-minute intervals
    best_interval = overall_minute_analysis.loc[overall_minute_analysis['Avg_Profit'].idxmax()]
    worst_interval = overall_minute_analysis.loc[overall_minute_analysis['Avg_Profit'].idxmin()]
    
    print(f"\nBest 10-minute interval in lower profit hours:")
    print(f"  {best_interval['Minute_Bin']}: Avg Profit = {best_interval['Avg_Profit']:.2f}, Win Rate = {best_interval['Win_Rate']:.1f}%")
    
    print(f"\nWorst 10-minute interval in lower profit hours:")
    print(f"  {worst_interval['Minute_Bin']}: Avg Profit = {worst_interval['Avg_Profit']:.2f}, Win Rate = {worst_interval['Win_Rate']:.1f}%")
    
    # Save the detailed analysis to CSV
    minute_analysis_sorted.to_csv('lower_hours_10min_analysis.csv', index=False)
    overall_minute_analysis.to_csv('overall_10min_lower_hours.csv', index=False)
    
else:
    print("\nNo data available for lower profit hours analysis.")

# Analyze consecutive trades
df = df.sort_values('Entry DateTime')
df['Next_Trade_Result'] = df['Trade_Result'].shift(-1)
df['Prev_Trade_Result'] = df['Trade_Result'].shift(1)

# After a win, what's the probability of the next trade being a win?
win_after_win = df[df['Prev_Trade_Result'] == 'Win']['Trade_Result'].value_counts(normalize=True)
# After a loss, what's the probability of the next trade being a win?
win_after_loss = df[df['Prev_Trade_Result'] == 'Loss']['Trade_Result'].value_counts(normalize=True)

print("\nTrade Sequence Analysis:")
print("Win probability after a win:")
print(win_after_win)
print("\nWin probability after a loss:")
print(win_after_loss)

# Analyze optimal stop loss levels
# Simulate different fixed stop loss levels
stop_loss_levels = [2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 15]
results = []

for sl in stop_loss_levels:
    # Create a copy of the dataframe for simulation
    sim_df = df.copy()
    
    # For stop loss trades, cap the loss at the simulated level
    sim_df.loc[sim_df['Exit Reason'] == 'Stop Loss', 'Simulated_Profit'] = -sl
    
    # For take profit trades, keep the original profit
    sim_df.loc[sim_df['Exit Reason'] == 'Take Profit', 'Simulated_Profit'] = sim_df.loc[sim_df['Exit Reason'] == 'Take Profit', 'Profit Points']
    
    # For other exit reasons, keep the original profit
    sim_df.loc[~sim_df['Exit Reason'].isin(['Stop Loss', 'Take Profit']), 'Simulated_Profit'] = sim_df.loc[~sim_df['Exit Reason'].isin(['Stop Loss', 'Take Profit']), 'Profit Points']
    
    # Calculate total profit and other metrics
    total_profit = sim_df['Simulated_Profit'].sum()
    avg_profit = sim_df[sim_df['Simulated_Profit'] > 0]['Simulated_Profit'].mean()
    avg_loss = abs(sim_df[sim_df['Simulated_Profit'] < 0]['Simulated_Profit'].mean())
    win_rate = (sim_df['Simulated_Profit'] > 0).mean() * 100
    
    results.append({
        'Stop Loss': sl,
        'Total Profit': total_profit,
        'Win Rate': win_rate,
        'Avg Profit': avg_profit,
        'Avg Loss': avg_loss,
        'Risk-Reward': avg_profit / avg_loss if avg_loss != 0 else float('inf')
    })

# Convert results to DataFrame for easier viewing
sl_results_df = pd.DataFrame(results)
print("\nSimulated Fixed Stop Loss Levels:")
print(sl_results_df)

# Analyze optimal take profit levels
# Simulate different fixed take profit levels
take_profit_levels = [2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 15]
tp_results = []

for tp in take_profit_levels:
    # Create a copy of the dataframe for simulation
    sim_df = df.copy()
    
    # For take profit trades, cap the profit at the simulated level
    sim_df.loc[sim_df['Exit Reason'] == 'Take Profit', 'Simulated_Profit'] = tp
    
    # For stop loss trades, keep the original loss
    sim_df.loc[sim_df['Exit Reason'] == 'Stop Loss', 'Simulated_Profit'] = sim_df.loc[sim_df['Exit Reason'] == 'Stop Loss', 'Profit Points']
    
    # For other exit reasons, keep the original profit
    sim_df.loc[~sim_df['Exit Reason'].isin(['Stop Loss', 'Take Profit']), 'Simulated_Profit'] = sim_df.loc[~sim_df['Exit Reason'].isin(['Stop Loss', 'Take Profit']), 'Profit Points']
    
    # Calculate total profit and other metrics
    total_profit = sim_df['Simulated_Profit'].sum()
    avg_profit = sim_df[sim_df['Simulated_Profit'] > 0]['Simulated_Profit'].mean()
    avg_loss = abs(sim_df[sim_df['Simulated_Profit'] < 0]['Simulated_Profit'].mean())
    win_rate = (sim_df['Simulated_Profit'] > 0).mean() * 100
    
    tp_results.append({
        'Take Profit': tp,
        'Total Profit': total_profit,
        'Win Rate': win_rate,
        'Avg Profit': avg_profit,
        'Avg Loss': avg_loss,
        'Risk-Reward': avg_profit / avg_loss if avg_loss != 0 else float('inf')
    })

# Convert results to DataFrame for easier viewing
tp_results_df = pd.DataFrame(tp_results)
print("\nSimulated Fixed Take Profit Levels:")
print(tp_results_df)

# Analyze optimal risk-reward ratios
# Simulate different risk-reward ratios
rr_ratios = [0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0, 2.5, 3.0]
rr_results = []

for rr in rr_ratios:
    # Create a copy of the dataframe for simulation
    sim_df = df.copy()
    
    # For each trade, calculate the take profit level based on the stop loss and risk-reward ratio
    for idx, row in sim_df.iterrows():
        if row['Exit Reason'] == 'Stop Loss':
            # This was a stop loss trade, so we know the stop loss level
            stop_loss = abs(row['Profit Points'])
            # Calculate what the take profit would have been with this risk-reward ratio
            take_profit = stop_loss * rr
            # We don't know if this trade would have hit take profit before stop loss
            # For simplicity, we'll assume the original outcome (stop loss)
            sim_df.at[idx, 'Simulated_Profit'] = row['Profit Points']
        elif row['Exit Reason'] == 'Take Profit':
            # This was a take profit trade
            # We'll cap the take profit at the simulated level based on the average stop loss
            avg_stop_loss = stop_loss_points.mean()
            simulated_take_profit = avg_stop_loss * rr
            # Cap the profit at the simulated level
            sim_df.at[idx, 'Simulated_Profit'] = min(row['Profit Points'], simulated_take_profit)
        else:
            # Other exit reasons, keep original profit
            sim_df.at[idx, 'Simulated_Profit'] = row['Profit Points']
    
    # Calculate total profit and other metrics
    total_profit = sim_df['Simulated_Profit'].sum()
    avg_profit = sim_df[sim_df['Simulated_Profit'] > 0]['Simulated_Profit'].mean()
    avg_loss = abs(sim_df[sim_df['Simulated_Profit'] < 0]['Simulated_Profit'].mean())
    win_rate = (sim_df['Simulated_Profit'] > 0).mean() * 100
    
    rr_results.append({
        'Risk-Reward Ratio': rr,
        'Total Profit': total_profit,
        'Win Rate': win_rate,
        'Avg Profit': avg_profit,
        'Avg Loss': avg_loss,
        'Actual Risk-Reward': avg_profit / avg_loss if avg_loss != 0 else float('inf')
    })

# Convert results to DataFrame for easier viewing
rr_results_df = pd.DataFrame(rr_results)
print("\nSimulated Risk-Reward Ratios:")
print(rr_results_df)

# Analyze trailing stop loss potential
# For simplicity, we'll simulate a basic trailing stop that moves the stop loss to breakeven after price moves in favor by X points
breakeven_points = [2, 3, 4, 5, 6, 8, 10]
be_results = []

for be_points in breakeven_points:
    # Create a copy of the dataframe for simulation
    sim_df = df.copy()
    
    # For stop loss trades, check if price moved in favor by be_points before hitting stop loss
    # This is a simplification as we don't have the price path data
    # We'll assume that if the trade duration is longer than average, price might have moved in our favor
    avg_duration = df['Duration Seconds'].mean()
    
    for idx, row in sim_df.iterrows():
        if row['Exit Reason'] == 'Stop Loss' and row['Duration Seconds'] > avg_duration:
            # This was a longer-duration stop loss trade
            # We'll assume there's a 50% chance price moved in our favor by be_points before hitting stop loss
            # In that case, the stop loss would have been at breakeven (0)
            if np.random.random() < 0.5:  # 50% chance
                sim_df.at[idx, 'Simulated_Profit'] = 0  # Breakeven
            else:
                sim_df.at[idx, 'Simulated_Profit'] = row['Profit Points']  # Original loss
        else:
            # Keep original profit/loss
            sim_df.at[idx, 'Simulated_Profit'] = row['Profit Points']
    
    # Calculate total profit and other metrics
    total_profit = sim_df['Simulated_Profit'].sum()
    avg_profit = sim_df[sim_df['Simulated_Profit'] > 0]['Simulated_Profit'].mean()
    avg_loss = abs(sim_df[sim_df['Simulated_Profit'] < 0]['Simulated_Profit'].mean())
    win_rate = (sim_df['Simulated_Profit'] > 0).mean() * 100
    
    be_results.append({
        'Breakeven Points': be_points,
        'Total Profit': total_profit,
        'Win Rate': win_rate,
        'Avg Profit': avg_profit,
        'Avg Loss': avg_loss,
        'Risk-Reward': avg_profit / avg_loss if avg_loss != 0 else float('inf')
    })

# Convert results to DataFrame for easier viewing
be_results_df = pd.DataFrame(be_results)
print("\nSimulated Breakeven Trailing Stop:")
print(be_results_df)

# Yearly Performance Analysis
print("\n" + "="*80)
print("YEARLY PERFORMANCE ANALYSIS")
print("="*80)

# Extract year from Entry DateTime
df['Year'] = df['Entry DateTime'].dt.year

# Get unique years and sort them
years = sorted(df['Year'].unique())

yearly_metrics = []

for year in years:
    year_data = df[df['Year'] == year].copy()
    
    if len(year_data) == 0:
        continue
    
    # Calculate metrics for the year
    total_trades = len(year_data)
    winning_trades = len(year_data[year_data['Profit Points'] > 0])
    losing_trades = len(year_data[year_data['Profit Points'] < 0])
    
    # Total Return
    total_return = year_data['Profit Points'].sum()
    
    # Win Rate
    win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
    
    # Profit Factor
    gross_profit = year_data[year_data['Profit Points'] > 0]['Profit Points'].sum()
    gross_loss = abs(year_data[year_data['Profit Points'] < 0]['Profit Points'].sum())
    profit_factor = gross_profit / gross_loss if gross_loss != 0 else float('inf')
    
    # Calculate cumulative returns for drawdown analysis
    year_data = year_data.sort_values('Entry DateTime')
    year_data['Cumulative_Return'] = year_data['Profit Points'].cumsum()
    
    # Max Drawdown calculation
    running_max = year_data['Cumulative_Return'].expanding().max()
    drawdown = year_data['Cumulative_Return'] - running_max
    max_drawdown = abs(drawdown.min()) if len(drawdown) > 0 else 0
    max_drawdown_pct = (max_drawdown / running_max.max() * 100) if running_max.max() != 0 else 0
    
    # Annualized Return (assuming 252 trading days per year)
    # Calculate the number of trading days in the year
    trading_days = len(year_data['Entry DateTime'].dt.date.unique())
    if trading_days > 0 and total_return != 0:
        daily_return = total_return / trading_days
        annualized_return = daily_return * 252
        annualized_return_pct = (annualized_return / abs(total_return)) * 100 if total_return != 0 else 0
    else:
        annualized_return = 0
        annualized_return_pct = 0
    
    # Total Return Percentage (assuming starting capital, we'll use absolute values)
    total_return_pct = total_return  # Since we don't have starting capital, we'll show absolute return
    
    # Sharpe Ratio calculation
    if len(year_data) > 1:
        daily_returns = year_data['Profit Points']
        mean_return = daily_returns.mean()
        std_return = daily_returns.std()
        sharpe_ratio = (mean_return / std_return) * np.sqrt(252) if std_return != 0 else 0
    else:
        sharpe_ratio = 0
    
    # Sortino Ratio calculation (using only negative returns for downside deviation)
    negative_returns = year_data[year_data['Profit Points'] < 0]['Profit Points']
    if len(negative_returns) > 0:
        downside_deviation = negative_returns.std()
        sortino_ratio = (year_data['Profit Points'].mean() / downside_deviation) * np.sqrt(252) if downside_deviation != 0 else 0
    else:
        sortino_ratio = float('inf') if year_data['Profit Points'].mean() > 0 else 0
    
    # Calmar Ratio (Annual Return / Max Drawdown)
    calmar_ratio = abs(annualized_return / max_drawdown) if max_drawdown != 0 else float('inf')
    
    # Risk-to-Reward Ratio calculations
    avg_winning_trade = year_data[year_data['Profit Points'] > 0]['Profit Points'].mean() if winning_trades > 0 else 0
    avg_losing_trade = abs(year_data[year_data['Profit Points'] < 0]['Profit Points'].mean()) if losing_trades > 0 else 0
    risk_reward_ratio = avg_winning_trade / avg_losing_trade if avg_losing_trade != 0 else float('inf')
    
    # Expectancy calculation (combines win rate and risk-reward ratio)
    expectancy = (win_rate / 100 * avg_winning_trade) - ((100 - win_rate) / 100 * avg_losing_trade)
    
    yearly_metrics.append({
        'Year': year,
        'Total Return (Points)': round(total_return, 2),
        'Total Return (%)': round(total_return_pct, 2),
        'Annualized Return (%)': round(annualized_return_pct, 2),
        'Max Drawdown (%)': round(max_drawdown_pct, 2),
        'Win Rate (%)': round(win_rate, 2),
        'Profit Factor': round(profit_factor, 2),
        'Avg Winning Trade': round(avg_winning_trade, 2),
        'Avg Losing Trade': round(avg_losing_trade, 2),
        'Risk-Reward Ratio': round(risk_reward_ratio, 2),
        'Expectancy': round(expectancy, 2),
        'Sharpe Ratio': round(sharpe_ratio, 2),
        'Sortino Ratio': round(sortino_ratio, 2),
        'Calmar Ratio': round(calmar_ratio, 2),
        'Total Trades': total_trades
    })

# Convert to DataFrame for better display
yearly_df = pd.DataFrame(yearly_metrics)

# Display yearly metrics
for _, row in yearly_df.iterrows():
    print(f"\n{int(row['Year'])} Performance:")
    print("Metrics:")
    print("------------------------")
    print(f"Total Return (%)        {row['Total Return (%)']:>10.2f}")
    print(f"Annualized Return (%)   {row['Annualized Return (%)']:>10.2f}")
    print(f"Max Drawdown (%)        {row['Max Drawdown (%)']:>10.2f}")
    print(f"Win Rate (%)            {row['Win Rate (%)']:>10.2f}")
    print(f"Profit Factor           {row['Profit Factor']:>10.2f}")
    print(f"Avg Winning Trade       {row['Avg Winning Trade']:>10.2f}")
    print(f"Avg Losing Trade        {row['Avg Losing Trade']:>10.2f}")
    print(f"Risk-Reward Ratio       {row['Risk-Reward Ratio']:>10.2f}")
    print(f"Expectancy              {row['Expectancy']:>10.2f}")
    print(f"Sharpe Ratio            {row['Sharpe Ratio']:>10.2f}")
    print(f"Sortino Ratio           {row['Sortino Ratio']:>10.2f}")
    print(f"Calmar Ratio            {row['Calmar Ratio']:>10.2f}")
    print(f"Total Trades            {row['Total Trades']:>10}")

# Summary statistics across all years
print(f"\n{'='*50}")
print("MULTI-YEAR SUMMARY")
print("="*50)

if len(yearly_df) > 1:
    print(f"Average Annual Return (%): {yearly_df['Total Return (%)'].mean():.2f}")
    print(f"Best Year: {yearly_df.loc[yearly_df['Total Return (%)'].idxmax(), 'Year']:.0f} ({yearly_df['Total Return (%)'].max():.2f}%)")
    print(f"Worst Year: {yearly_df.loc[yearly_df['Total Return (%)'].idxmin(), 'Year']:.0f} ({yearly_df['Total Return (%)'].min():.2f}%)")
    print(f"Average Win Rate (%): {yearly_df['Win Rate (%)'].mean():.2f}")
    print(f"Average Profit Factor: {yearly_df['Profit Factor'].mean():.2f}")
    print(f"Average Winning Trade: {yearly_df['Avg Winning Trade'].mean():.2f}")
    print(f"Average Losing Trade: {yearly_df['Avg Losing Trade'].mean():.2f}")
    print(f"Average Risk-Reward Ratio: {yearly_df['Risk-Reward Ratio'].mean():.2f}")
    print(f"Average Expectancy: {yearly_df['Expectancy'].mean():.2f}")
    print(f"Average Sharpe Ratio: {yearly_df['Sharpe Ratio'].mean():.2f}")
    print(f"Total Trades (All Years): {yearly_df['Total Trades'].sum()}")
    
    # Consistency metrics
    positive_years = len(yearly_df[yearly_df['Total Return (%)'] > 0])
    total_years = len(yearly_df)
    consistency = (positive_years / total_years) * 100
    print(f"Profitable Years: {positive_years}/{total_years} ({consistency:.1f}%)")
    
    # Risk-Reward consistency metrics
    good_rr_years = len(yearly_df[yearly_df['Risk-Reward Ratio'] >= 1.0])
    rr_consistency = (good_rr_years / total_years) * 100
    print(f"Years with R:R >= 1.0: {good_rr_years}/{total_years} ({rr_consistency:.1f}%)")
    
    # Best and worst risk-reward years
    best_rr_year = yearly_df.loc[yearly_df['Risk-Reward Ratio'].idxmax()]
    worst_rr_year = yearly_df.loc[yearly_df['Risk-Reward Ratio'].idxmin()]
    print(f"Best R:R Year: {best_rr_year['Year']:.0f} (R:R = {best_rr_year['Risk-Reward Ratio']:.2f})")
    print(f"Worst R:R Year: {worst_rr_year['Year']:.0f} (R:R = {worst_rr_year['Risk-Reward Ratio']:.2f})")

# Save yearly analysis to CSV
yearly_df.to_csv('yearly_performance_analysis.csv', index=False)
print(f"\nYearly analysis saved to 'yearly_performance_analysis.csv'")

# Save the results to a CSV file for further analysis
sl_results_df.to_csv('stop_loss_simulation.csv', index=False)
tp_results_df.to_csv('take_profit_simulation.csv', index=False)
rr_results_df.to_csv('risk_reward_simulation.csv', index=False)
be_results_df.to_csv('breakeven_simulation.csv', index=False)